import uuid
import string
import secrets
import json  # 确保导入json模块
import hashlib
import zlib
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
# SQLAlchemy导入已移除，现在使用API服务
from db_models import logger  # 只保留logger
from utils.render_markdown import render_content_to_html
from record_api_client import record_api_client  # 导入Record API客户端

# 数据库会话已移除，现在使用API服务

def verify_api_key(api_key: str) -> Optional[Dict[str, Any]]:
    """验证API密钥并返回用户数据"""
    try:
        return record_api_client.verify_api_key(api_key)
    except Exception as e:
        logger.error(f"验证API密钥失败: {e}")
        return None


# =================== 用户相关操作 ===================

def get_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    """获取用户信息（返回字典而非ORM对象）"""
    try:
        return record_api_client.get_user_data(user_id)
    except Exception as e:
        logger.error(f"获取用户数据失败: {e}")
        return None


def get_user_temperature(user_id: int, default: float = 0.7) -> float:
    """获取用户当前温度设置"""
    try:
        return record_api_client.get_user_temperature(user_id, default)
    except Exception as e:
        logger.error(f"获取用户温度设置失败: {e}")
        return default


def get_user_model_name(user_id: int) -> Optional[str]:
    """获取用户当前模型名称"""
    try:
        return record_api_client.get_user_model_name(user_id, "gpt-3.5-turbo")
    except Exception as e:
        logger.error(f"获取用户模型名称失败: {e}")
        return "gpt-3.5-turbo"

def get_user_model_id(user_id: int) -> Optional[int]:
    """获取用户当前模型ID（已废弃，保留兼容性）"""
    # 这个函数保留是为了兼容性，但不再使用
    return None


def update_user_model_name_preference(user_id: int, model_name: str) -> bool:
    """更新用户的模型名称偏好设置"""
    try:
        result = record_api_client.update_user(user_id, current_model_name=model_name)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户模型名称偏好失败: {e}")
        return False

def update_user_model_preference(user_id: int, model_id: int) -> bool:
    """更新用户的模型偏好设置（已废弃，保留兼容性）"""
    # 这个函数保留是为了兼容性，但不再使用
    return True


def update_user_temperature_preference(user_id: int, temperature: float) -> bool:
    """更新用户的温度设置"""
    try:
        result = record_api_client.update_user(user_id, current_temperature=temperature)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户温度设置失败: {e}")
        return False


def update_user_conversation_preference(user_id: int, conversation_id: int) -> bool:
    """更新用户的对话偏好设置"""
    try:
        result = record_api_client.update_user(user_id, current_conversation_id=conversation_id)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户对话偏好失败: {e}")
        return False


def update_user_mathjax_preference(user_id: int, use_mathjax: bool) -> bool:
    """更新用户MathJax渲染偏好"""
    try:
        result = record_api_client.update_user(user_id, mathjax=use_mathjax)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户MathJax偏好失败: {e}")
        return False


def get_user_statistics(user_id: int) -> Optional[Dict[str, Any]]:
    """获取用户统计数据"""
    try:
        return record_api_client.get_user_statistics(user_id)
    except Exception as e:
        logger.error(f"获取用户统计数据失败: {e}")
        return None


# =================== 模型相关操作（已移除数据库依赖，改为API服务）===================
# 这些函数保留是为了兼容性，但实际功能已移至API服务

# 模型相关函数已移除，请使用API服务获取模型信息
# 如需获取模型列表，请使用 callbacks/model_callbacks.py 中的 get_available_models_from_api()

def get_AU_from_model_name(model_name: str) -> Tuple[str, str]:
    """根据模型名称获取API配置（使用新的API服务）"""
    # 现在所有模型都使用统一的API服务
    from config import config
    return config.MODEL_API_KEY, f"{config.MODEL_API_BASE_URL}/api/v1"


# =================== 对话相关操作 ===================

def get_conversation_data(conversation_id: int) -> Optional[Dict[str, Any]]:
    """获取指定ID的对话数据（字典形式）"""
    try:
        return record_api_client.get_conversation_data(conversation_id)
    except Exception as e:
        logger.error(f"获取对话失败: {e}")
        return None


def get_user_conversations_list(user_id: int) -> List[Dict[str, Any]]:
    """获取用户的所有对话（字典列表）"""
    try:
        return record_api_client.get_user_conversations_list(user_id)
    except Exception as e:
        logger.error(f"获取用户对话列表失败: {e}")
        return []


def create_new_conversation(user_id: int, title: str = '新对话') -> Optional[Dict[str, Any]]:
    """创建新对话并返回数据字典"""
    try:
        return record_api_client.create_new_conversation(user_id, title)
    except Exception as e:
        logger.error(f"创建新对话失败: {e}")
        return None


def update_conversation_title(conversation_id: int, title: str) -> bool:
    """更新对话标题"""
    try:
        return record_api_client.update_conversation_title(conversation_id, title)
    except Exception as e:
        logger.error(f"更新对话标题失败: {e}")
        return False


def delete_conversation(conversation_id: int) -> bool:
    """删除指定ID的对话及其所有消息"""
    try:
        return record_api_client.delete_conversation(conversation_id)
    except Exception as e:
        logger.error(f"删除对话失败: {e}")
        return False


def set_current_conversation(user_id: int, conversation_id: int) -> bool:
    """设置用户的当前对话"""
    try:
        return record_api_client.set_current_conversation(user_id, conversation_id)
    except Exception as e:
        logger.error(f"设置当前对话失败: {e}")
        return False


# =================== 消息相关操作 ===================

def get_message_data(message_id: int) -> Optional[Dict[str, Any]]:
    """获取指定消息数据（字典形式）"""
    try:
        return record_api_client.get_message_data(message_id)
    except Exception as e:
        logger.error(f"获取消息数据失败: {e}")
        return None


def get_conversation_messages_list(conversation_id: int) -> List[Dict[str, Any]]:
    """获取对话的所有消息（字典列表）"""
    try:
        return record_api_client.get_conversation_messages_list(conversation_id)
    except Exception as e:
        logger.error(f"获取对话消息列表失败: {e}")
        return []


def load_conversation_messages_by_id(conversation_id: int) -> List[Dict[str, str]]:
    """加载对话历史，转换为AI接口格式"""
    try:
        return record_api_client.load_conversation_messages_by_id(conversation_id)
    except Exception as e:
        logger.error(f"加载对话历史失败: {e}")
        return []


def update_message_token_count(message_id: int, prompt_tokens: int, completion_tokens: int) -> bool:
    """更新消息的token计数"""
    try:
        return record_api_client.update_message_token_count(message_id, prompt_tokens, completion_tokens)
    except Exception as e:
        logger.error(f"更新消息token计数失败: {e}")
        return False


def delete_message(message_id: int) -> bool:
    """删除指定ID的单条消息"""
    try:
        return record_api_client.delete_message(message_id)
    except Exception as e:
        logger.error(f"删除消息失败: {e}")
        return False


def create_message(conversation_id, role, content,
                  model_name=None, temperature=None,
                  max_tokens=None, is_error=False,
                  error_info=None,
                  prompt_tokens=None,
                  completion_tokens=None,
                  # 保留model_id参数以兼容旧代码
                  model_id=None):
    """创建新消息"""
    try:
        # 准备消息数据
        message_data = {
            'conversation_id': conversation_id,
            'role': role,
            'content': content,
            'model_id': model_id or 1,  # 默认模型ID，Record API需要
            'is_error': is_error
        }

        # 添加可选参数
        if temperature is not None:
            message_data['temperature'] = float(temperature)
        if max_tokens is not None:
            message_data['max_tokens'] = max_tokens
        if error_info is not None:
            message_data['error_info'] = error_info
        if prompt_tokens is not None:
            message_data['prompt_tokens'] = prompt_tokens
        if completion_tokens is not None:
            message_data['completion_tokens'] = completion_tokens

        return record_api_client.create_message(**message_data)
    except Exception as e:
        logger.error(f"创建消息失败: {e}")
        return None


# =================== 账户金额相关 ===================

def deposit_to_user(user_id: int, amount: float, description: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """为用户充值"""
    try:
        # 通过API为用户充值
        result = record_api_client.deposit_to_user(
            user_id=user_id,
            amount=amount,
            description=description or f"充值 ${amount}"
        )

        if result:
            logger.info(f"用户 {user_id} 成功充值 ${amount}")
            return get_user_data(user_id)
        return None
    except Exception as e:
        logger.error(f"用户充值失败: {e}")
        return None


def check_user_balance(user_id: int, model_name: str, estimated_tokens: int = 1000) -> Tuple[bool, str]:
    """检查用户余额是否足够支付预估的token消费（使用默认价格）"""
    try:
        user = record_api_client.get_user_data(user_id)
        if not user:
            return False, "用户不存在"

        # 使用默认价格进行预估（每1000个token的价格）
        # 这些价格可以根据实际情况调整
        default_input_price = 0.001  # $0.001 per 1000 tokens
        default_output_price = 0.002  # $0.002 per 1000 tokens

        # 根据模型名称调整价格
        if "gpt-4" in model_name.lower():
            default_input_price = 0.03   # GPT-4价格更高
            default_output_price = 0.06
        elif "claude" in model_name.lower():
            default_input_price = 0.008
            default_output_price = 0.024

        # 计算预估成本 (假设输入输出token数量相等)
        estimated_cost = (default_input_price + default_output_price) * estimated_tokens / 1000

        # 检查余额
        current_balance = float(user.get('current_balance', 0.0))
        if current_balance < estimated_cost:
            return False, f"余额不足。当前余额: ${current_balance}, 预估成本: ${estimated_cost}"

        return True, "余额充足"
    except Exception as e:
        logger.error(f"检查用户余额失败: {e}")
        return False, f"系统错误: {str(e)}"


def calculate_message_cost(message_id: int, prompt_tokens: int = None, completion_tokens: int = None, model_name: str = None) -> Optional[float]:
    """计算消息成本并更新用户余额（使用默认价格）"""
    try:
        # 如果没有提供token信息，尝试从消息数据中获取
        if prompt_tokens is None or completion_tokens is None:
            message_data = record_api_client.get_message_data(message_id)
            if not message_data:
                logger.warning(f"未找到消息ID: {message_id}")
                return None

            prompt_tokens = prompt_tokens or message_data.get('prompt_tokens', 0)
            completion_tokens = completion_tokens or message_data.get('completion_tokens', 0)
            model_name = model_name or message_data.get('model_name', 'gpt-3.5-turbo')

        # 根据模型名称设置价格
        if "gpt-4" in model_name.lower():
            input_price = 0.03   # GPT-4价格更高
            output_price = 0.06
        elif "claude" in model_name.lower():
            input_price = 0.008
            output_price = 0.024
        else:
            input_price = 0.001  # 默认价格 (GPT-3.5-turbo)
            output_price = 0.002

        # 通过API计算消息成本
        result = record_api_client.calculate_message_cost(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            input_price=input_price,
            output_price=output_price
        )

        if result:
            total_cost = result.get('total_cost')

            # 更新消息的成本信息
            update_data = {
                'prompt_cost': result.get('prompt_cost'),
                'completion_cost': result.get('completion_cost'),
                'total_cost': total_cost
            }
            record_api_client.update_message(message_id, **update_data)

            return total_cost
        return None
    except Exception as e:
        logger.error(f"计算消息成本失败: {e}")
        return None


# =================== 事务相关操作 ===================

def get_user_transactions(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """获取用户的交易记录"""
    try:
        return record_api_client.get_user_transactions(user_id, limit, offset)
    except Exception as e:
        logger.error(f"获取用户交易记录失败: {e}")
        return []


# =================== 用户管理相关 ===================

def generate_api_key(prefix: str = "sk-") -> str:
    """生成一个唯一的API密钥"""
    # 生成一个随机的UUID部分
    uuid_part = str(uuid.uuid4()).replace('-', '')

    # 生成一个随机的字符串部分（16个字符）
    alphabet = string.ascii_letters + string.digits
    random_part = ''.join(secrets.choice(alphabet) for _ in range(16))
    # 组合前缀和两个随机部分
    api_key = f"{prefix}{uuid_part}{random_part}"
    return api_key


def create_user(
        permission: int = 1,
        current_model_name: str = "gpt-3.5-turbo",
        initial_balance: float = 10.0,
        # 保留current_model_id参数以兼容旧代码
        current_model_id: int = None) -> (
        Tuple)[Optional[Dict[str, Any]], Optional[str]]:
    """创建新用户"""
    try:
        # 生成API密钥
        api_key = generate_api_key()

        # 准备用户数据
        user_data = {
            'api_key': api_key,
            'permission': permission,
            'current_balance': initial_balance,
            'total_deposited': initial_balance,
            'current_temperature': 0.7,
            'mathjax': False
        }

        # 创建用户
        user = record_api_client.create_user(**user_data)
        if not user:
            return None, None

        # 创建初始对话
        title = "欢迎使用AI对话系统" if permission == 9 else "开始使用AI助手"
        conversation = record_api_client.create_conversation(user['id'], title)
        if not conversation:
            return None, None

        # 设置为当前对话
        record_api_client.set_current_conversation(user['id'], conversation['id'])

        # 如果是管理员，添加初始消息
        if permission == 9:
            record_api_client.create_message(
                conversation_id=conversation['id'],
                role="assistant",
                content="欢迎使用AI对话系统！我已准备好回答您的问题。",
                model_id=1,  # 默认模型ID
                prompt_tokens=0,
                completion_tokens=0,
                total_cost=0.0
            )

        logger.info(f"用户已创建，权限级别: {permission}, API密钥: {api_key}")

        # 返回用户数据和API密钥
        return user, api_key

    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        return None, None


# =================== 管理员功能 ===================

def is_admin_user(user_id: int) -> bool:
    """检查用户是否具有管理员权限 (permission=9)"""
    try:
        user = record_api_client.get_user_data(user_id)
        return user is not None and user.get('permission') == 9
    except Exception as e:
        logger.error(f"检查管理员权限失败: {e}")
        return False


def get_all_users(admin_id: int) -> Optional[List[Dict[str, Any]]]:
    """获取所有用户列表（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试访问用户列表")
        return None

    try:
        users_data = record_api_client.get_users(per_page=100)
        if not users_data or 'users' not in users_data:
            return []

        users = users_data['users']
        return [{
            "id": user.get("id"),
            "api_key": user.get("api_key", "")[:10] + "..." + user.get("api_key", "")[-5:],  # 只返回部分API密钥
            "permission": user.get("permission", 1),
            "is_active": user.get("is_active", True),
            "current_balance": float(user.get("current_balance", 0.0)),
            "total_spent": float(user.get("total_spent", 0.0)),
            "total_deposited": float(user.get("total_deposited", 0.0)),
            "created_at": user.get("created_at"),
        } for user in users]
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        return None


def toggle_user_active_status(admin_id: int, user_id: int) -> bool:
    """切换用户活跃状态（启用/禁用用户）(仅限管理员)"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试切换用户 {user_id} 的活跃状态")
        return False

    try:
        user = record_api_client.get_user_data(user_id)
        if not user:
            return False

        # 不允许禁用自己（管理员）
        if user.get('id') == admin_id:
            logger.warning(f"管理员 {admin_id} 尝试禁用自己的账户")
            return False

        # 切换状态
        new_status = not user.get('is_active', True)
        result = record_api_client.update_user(user_id, is_active=new_status)
        return result is not None
    except Exception as e:
        logger.error(f"切换用户活跃状态失败: {e}")
        return False


def admin_add_user_balance(admin_id: int, user_id: int, amount: float, description: str = None) -> bool:
    """管理员为用户充值（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试为用户 {user_id} 充值")
        return False

    if amount <= 0:
        return False

    try:
        user = record_api_client.get_user_data(user_id)
        if not user:
            return False

        # 计算新余额
        current_balance = float(user.get('current_balance', 0.0))
        total_deposited = float(user.get('total_deposited', 0.0))
        new_balance = current_balance + amount
        new_total_deposited = total_deposited + amount

        # 更新用户余额
        result = record_api_client.update_user(
            user_id,
            current_balance=new_balance,
            total_deposited=new_total_deposited
        )

        if result:
            logger.info(f"管理员 {admin_id} 为用户 {user_id} 充值 ${amount}，用户当前余额: ${new_balance}")
            return True
        return False
    except Exception as e:
        logger.error(f"管理员充值失败: {e}")
        return False


def admin_create_new_user(
        admin_id: int,
        permission: int = 1,
        initial_balance: float = 10.0) \
        -> Optional[Tuple[Dict[str, Any], str]]:
    """管理员创建新用户（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试创建用户")
        return None

    # 使用现有的create_user函数和默认模型名称
    return create_user(permission=permission, initial_balance=initial_balance, current_model_name="gpt-3.5-turbo")



# 在 db_operator.py 中添加和修改以下函数

import hashlib
import zlib
from utils.render_markdown import render_content_to_html

def get_content_hash(content):
    """计算内容的哈希值"""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def get_message_content(message_id):
    """获取消息内容"""
    try:
        message_data = record_api_client.get_message_data(message_id)
        if message_data:
            return message_data.get('content')
        return None
    except Exception as e:
        logger.error(f"获取消息内容失败: {e}")
        return None

def update_message_content(message_id, new_content, is_error=False, error_info=None):
    """更新消息内容"""
    try:
        # 准备更新数据
        update_data = {
            'content': new_content,
            'is_error': is_error
        }

        if error_info is not None:
            update_data['error_info'] = error_info

        # 通过API更新消息
        result = record_api_client.update_message(message_id, **update_data)
        return result
    except Exception as e:
        logger.error(f"更新消息内容失败: {e}")
        return None

# create_message函数已在上面定义，这里删除重复定义

# 渲染内容相关函数
def store_rendered_content(message_id, content):
    """存储消息的渲染内容到MinIO"""
    try:
        # 检查消息是否存在
        message_data = record_api_client.get_message_data(message_id)
        if not message_data:
            logger.warning(f"未找到消息ID: {message_id}")
            return False

        # 使用MinIO处理并存储消息内容
        result = record_api_client.process_and_store_message_content(message_id, content)

        if result:
            logger.info(f"消息 {message_id} 内容已存储到MinIO: {result}")
            return True
        else:
            logger.error(f"消息 {message_id} 内容存储失败")
            return False

    except Exception as e:
        logger.error(f"存储渲染内容失败: {e}")
        return False

def get_rendered_content(message_id, mathjax=True):
    """获取消息的渲染内容URL"""
    try:
        # 返回MinIO中的渲染内容URL
        url = record_api_client.get_rendered_content(message_id, mathjax)
        return url
    except Exception as e:
        logger.error(f"获取渲染内容URL失败: {e}")
        return None

def clear_rendered_content(message_id):
    """清除消息的渲染缓存"""
    try:
        result = record_api_client.clear_rendered_content(message_id)
        return result is not None
    except Exception as e:
        logger.error(f"清除渲染缓存失败: {e}")
        return False
