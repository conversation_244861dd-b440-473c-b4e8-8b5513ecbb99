# openaiSDK.py (修改版)
import logging
import requests
from openai import OpenAI
import json

from db_operator import (
    get_user_data,
    load_conversation_messages_by_id,
    update_message_token_count,
    calculate_message_cost,
    get_conversation_messages_list,
    create_message,
    get_user_model_name
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OpenAISDK:

    @staticmethod
    def extract_text_from_multimodal(content):
        """从多模态内容中提取纯文本"""
        try:
            if isinstance(content, str):
                try:
                    content_obj = json.loads(content)
                except json.JSONDecodeError:
                    # 不是JSON格式，直接返回原始文本
                    return content
            else:
                content_obj = content

            if isinstance(content_obj, list):
                texts = []
                for item in content_obj:
                    if item.get("type") == "text":
                        texts.append(item.get("text", ""))
                return " ".join(texts)

            return content
        except Exception as e:
            logger.error(f"提取文本内容出错: {e}")
            # 如果处理出错，返回原始内容
            return content if isinstance(content, str) else str(content)

    @staticmethod
    def process_content_for_api(content, role, supports_vision=True):
        """
        处理消息内容，根据角色和模型能力调整格式

        Args:
            content: 消息内容（字符串或JSON字符串）
            role: 消息角色 (user, assistant, system)
            supports_vision: 模型是否支持图片输入
        """
        try:
            # assistant 和 system 角色消息始终使用纯文本格式
            if role in ["assistant", "system"]:
                # 如果内容已经是字符串，直接返回
                if isinstance(content, str):
                    # 检查是否为JSON格式的存储内容 [{"type": "text", "text": "..."}]
                    try:
                        content_obj = json.loads(content)
                        if isinstance(content_obj, list) and content_obj and content_obj[0].get("type") == "text":
                            # 提取并返回文本内容
                            return content_obj[0].get("text", "")
                    except json.JSONDecodeError:
                        # 不是JSON格式，直接返回原始文本
                        return content
                else:
                    # 非字符串内容转为字符串
                    try:
                        return str(content)
                    except:
                        return "无法解析的内容"

            # 以下处理用户消息
            # 如果模型不支持图片，直接提取纯文本
            if not supports_vision:
                return OpenAISDK.extract_text_from_multimodal(content)

            # 模型支持图片，处理为多模态格式
            if isinstance(content, str):
                try:
                    content_obj = json.loads(content)
                except json.JSONDecodeError:
                    # 不是JSON格式，将纯文本转换为新格式
                    return [{"type": "text", "text": content}]
            else:
                content_obj = content

            # 如果已经是多模态列表格式
            if isinstance(content_obj, list):
                processed_content = []
                for item in content_obj:
                    # 处理文本项
                    if item.get("type") == "text":
                        processed_content.append(item)
                    # 处理图片URL项 - 这里图片已经是base64格式，不需要转换
                    elif item.get("type") == "image_url" and "image_url" in item:
                        processed_content.append(item)
                return processed_content
            else:
                # 如果是其他格式，转换为文本
                return [{"type": "text", "text": str(content_obj)}]

        except Exception as e:
            logger.error(f"处理内容异常: {e}, role={role}")
            # 根据角色决定返回格式
            if role == "assistant":
                return str(content) if isinstance(content, str) else "处理内容时出错"
            elif supports_vision:
                return [{"type": "text", "text": str(content)}]
            else:
                return str(content)

    def __init__(self, user_id=None, model_name=None, api_key=None):
        from config import config
        self.user_id = user_id
        self.model_name = model_name
        self.api_key = api_key or config.MODEL_API_KEY
        self.base_url = f"{config.MODEL_API_BASE_URL}/api/v1"
        self.user_data = None
        self.current_message_id = None
        self.supports_vision = False

        # 如果提供了用户ID，获取用户数据
        if self.user_id:
            self.load_user_data()

    def load_user_data(self):
        """加载用户数据和当前设置"""
        try:
            # 重新获取最新的用户数据
            self.user_data = get_user_data(self.user_id)
            if not self.user_data:
                raise ValueError(f"未找到用户ID: {self.user_id}")

            # 获取用户当前对话
            self.conversation_id = self.user_data.get("current_conversation_id")
            if not self.conversation_id:
                # 如果没有当前对话，尝试获取用户的第一个对话
                conversations = get_user_conversations_list(self.user_id)
                if conversations:
                    # 使用第一个对话作为当前对话
                    first_conversation = conversations[0]
                    self.conversation_id = first_conversation.get("id")
                    # 更新用户的当前对话
                    from db_operator import set_current_conversation
                    set_current_conversation(self.user_id, self.conversation_id)
                    logger.info(f"自动设置用户 {self.user_id} 的当前对话为: {self.conversation_id}")
                else:
                    raise ValueError(f"用户没有当前对话，请先创建对话")

            # 获取用户当前模型名称（如果没有传入模型名称）
            if not self.model_name:
                self.model_name = get_user_model_name(self.user_id)
                if not self.model_name:
                    self.model_name = "gpt-3.5-turbo"  # 默认模型
                    logger.warning(f"用户 {self.user_id} 没有设置模型，使用默认模型: {self.model_name}")

            # 根据模型名称判断是否支持图片输入
            self.supports_vision = self._check_vision_support(self.model_name)
            logger.info(f"当前模型 {self.model_name} 支持图片输入: {self.supports_vision}")

        except Exception as e:
            logger.error(f"加载用户数据失败: {e}")
            raise

    def _check_vision_support(self, model_name):
        """根据模型名称判断是否支持图片输入"""
        vision_models = [
            "gpt-4-vision", "gpt-4o", "gpt-4o-mini",
            "claude-3", "gemini-pro-vision"
        ]
        return any(vision_model in model_name.lower() for vision_model in vision_models)

    def generate_response_sync(self) -> str:
        """同步生成AI响应，支持多模态内容，并直接保存到数据库"""
        if not self.conversation_id:
            raise ValueError("未指定对话ID")

        try:
            # 获取历史消息
            db_messages = load_conversation_messages_by_id(self.conversation_id)
            if not db_messages:
                raise ValueError(f"对话 {self.conversation_id} 没有消息")

            # 使用当前模型名称
            model_name = self.model_name
            temperature = float(self.user_data.get("current_temperature", 0.7))

            # 确定模型是否支持图片输入
            supports_vision = self.supports_vision

            api_messages = []
            for msg in db_messages:
                role = msg["role"]
                content = msg["content"]

                # 根据模型能力和角色处理内容
                processed_content = self.process_content_for_api(
                    content,
                    role=role,
                    supports_vision=supports_vision
                )

                api_messages.append({
                    "role": role,
                    "content": processed_content
                })

            # 记录发送到API的消息
            logger.info(f"发送到API的消息数量: {len(api_messages)}")
            logger.info(f"模型 {model_name} 支持图片输入: {supports_vision}")

            # 使用新的API服务
            logger.info("正在使用新的API服务...")

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model_name,
                "messages": api_messages,
                "temperature": temperature,
                "stream": False
            }

            # 发送请求到新的API服务
            api_url = f"{self.base_url}/chat/completions"
            api_response = requests.post(api_url, headers=headers, json=payload, timeout=180)
            api_response.raise_for_status() # 如果请求失败则抛出异常
            
            response_data = api_response.json()

            # 从返回的JSON中提取信息
            ai_message = response_data['choices'][0]['message']['content']
            prompt_tokens = response_data['usage']['prompt_tokens']
            completion_tokens = response_data['usage']['completion_tokens']

            # 获取最新添加的消息ID（假设是用户消息）
            messages_list = get_conversation_messages_list(self.conversation_id)

            if messages_list and len(messages_list) > 0:
                # 找到最后一条用户消息
                for msg in reversed(messages_list):
                    if msg["role"] == "user":
                        self.current_message_id = msg["id"]
                        break

            # 更新消息的token使用情况
            if self.current_message_id:
                update_message_token_count(
                    self.current_message_id,
                    prompt_tokens,
                    completion_tokens
                )
                # 计算消息成本
                calculate_message_cost(self.current_message_id)

            # 创建AI响应内容
            ai_response_content = []
            ai_response_content.append({
                "type": "text",
                "text": ai_message
            })

            # 直接将AI响应添加到数据库（使用模型名称）
            create_message(
                conversation_id=self.conversation_id,
                role="assistant",
                content=json.dumps(ai_response_content, ensure_ascii=False),
                model_name=model_name,
                temperature=temperature,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens
            )

            return ai_message

        except Exception as e:
            logger.error(f"生成响应失败: {e}")
            # 当发生异常时，也将错误消息保存到数据库
            error_message = f"抱歉，我遇到了一个问题：{str(e)}"
            error_content = json.dumps([{"type": "text", "text": error_message}], ensure_ascii=False)

            create_message(
                conversation_id=self.conversation_id,
                role="assistant",
                content=error_content,
                model_name=self.model_name,
                temperature=temperature if 'temperature' in locals() else None,
                is_error=True,
                error_info=str(e)
            )

            return error_message