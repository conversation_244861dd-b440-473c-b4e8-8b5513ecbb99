# config.py - 项目配置文件
import os

class Config:
    """应用配置类"""

    # =================== 数据库配置 ===================
    # MariaDB配置（用于模型管理）
    MARIADB_USER = os.environ.get('MARIADB_USER', 'root')
    MARIADB_PASSWORD = os.environ.get('MARIADB_PASSWORD', 'rw80827')
    MARIADB_HOST = os.environ.get('MARIADB_HOST', '*************')
    MARIADB_PORT = os.environ.get('MARIADB_PORT', '3306')
    MARIADB_MODEL_DB = os.environ.get('MARIADB_MODEL_DB', 'model_registry')
    MARIADB_CHAT_DB = os.environ.get('MARIADB_CHAT_DB', 'chat_system')

    # =================== API服务配置 ===================
    # Model API Service (端口5002) - 模型和聊天API
    MODEL_API_BASE_URL = os.environ.get('MODEL_API_BASE_URL', 'http://localhost:5002')
    MODEL_API_KEY = os.environ.get('MODEL_API_KEY', 'sk-default-api-key-change-in-production')

    # Record API Service (端口5003) - 用户、对话、消息记录API
    RECORD_API_BASE_URL = os.environ.get('RECORD_API_BASE_URL', 'http://localhost:5003')
    RECORD_API_KEY = os.environ.get('RECORD_API_KEY', 'admin-api-key-change-in-production')

    # =================== 应用配置 ===================
    # Dash应用配置
    DASH_HOST = os.environ.get('DASH_HOST', '0.0.0.0')
    DASH_PORT = int(os.environ.get('DASH_PORT', '8050'))
    DASH_DEBUG = os.environ.get('DASH_DEBUG', 'False').lower() == 'true'

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')

    # =================== 安全配置 ===================
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
    JWT_ALGORITHM = os.environ.get('JWT_ALGORITHM', 'HS256')
    JWT_EXPIRATION_HOURS = int(os.environ.get('JWT_EXPIRATION_HOURS', '24'))

    @classmethod
    def get_model_api_url(cls) -> str:
        """获取模型API的完整URL"""
        return cls.MODEL_API_BASE_URL.rstrip('/')

    @classmethod
    def get_record_api_url(cls) -> str:
        """获取记录API的完整URL"""
        return cls.RECORD_API_BASE_URL.rstrip('/')

    @classmethod
    def get_model_api_headers(cls) -> dict:
        """获取模型API请求头"""
        return {
            'Authorization': f'Bearer {cls.MODEL_API_KEY}',
            'Content-Type': 'application/json'
        }

    @classmethod
    def get_record_api_headers(cls) -> dict:
        """获取记录API请求头"""
        return {
            'Authorization': f'Bearer {cls.RECORD_API_KEY}',
            'Content-Type': 'application/json'
        }


# 全局配置实例
config = Config()
