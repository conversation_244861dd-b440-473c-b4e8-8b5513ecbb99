"""
Inspirflow应用主布局文件
定义整个应用的布局结构，组织和连接各个UI组件
"""

from dash import html, dcc, clientside_callback, ClientsideFunction
from dash.dependencies import Input, Output

# 导入组件模块
from components import (
    create_navbar,
    create_api_key_area,
    create_sidebar,
    create_model_selection_panel,
    create_chat_area,
    create_admin_panel,
    create_add_user_modal,
    create_api_key_display_modal,
    create_edit_message_modal,
    create_delete_conversation_modal
)


def create_global_stores() -> html.Div:
    """
    创建应用的全局存储组件集合

    Returns:
        html.Div: 包含所有dcc.Store组件的容器
    """
    return html.Div([
        dcc.Store(id="store-user-apikey"),
        dcc.Store(id="store-user-id"),
        dcc.Store(id="store-user-current-temperature"),
        dcc.Store(id="store-user-current-model-name"),
        dcc.Store(id="store-user-current-conversation-id"),
        dcc.Store(id="store-conversation-list", data={}),
        dcc.Store(id="store-conversation-selected", data=None),
        dcc.Store(id="store-conversation-checked-list", data=[]),
        dcc.Store(id="store-edited-message", data=None),
        dcc.Store(id="store-user-use-mathjax", data=True),
        dcc.Store(id="store-uploaded-images", data=[]),
        dcc.Store(id="store-user-permission", data=1),
        dcc.Store(id="store-selected-user-for-action", data=None),
        dcc.Store(id='store-image-urls', data=[]),
        dcc.Store(id="messages-update-event", storage_type="memory"),
        dcc.Interval(id="interval-component",interval=2000, n_intervals=0,disabled=False),
        # 添加一个存储组件用于存储等待中的响应信息
        dcc.Store(id="store-pending-response"),
        # 自动刷新定时器
        dcc.Interval(id="conversation-refresh", interval=30 * 1000),  # 30秒
        dcc.Interval(id="user-stats-refresh", interval=10 * 1000),  # 10秒
    ])
def create_app_layout() -> html.Div:
    """
    创建整个应用的布局
    组织所有UI组件并定义整体结构

    Returns:
        html.Div: 应用的根布局容器
    """
    return html.Div([
        # 客户端脚本控制
        html.Div(id="clientside-iframe-script-output"),
        html.Div(id="clientside-iframe-script-trigger", children="trigger", style={"display": "none"}),
        dcc.Location(id='url', refresh=False),

        # 导航栏
        create_navbar(),

        # 内容区域包装
        html.Div([
            # API Key验证区域
            create_api_key_area(),

            # 主要内容区域
            html.Div([
                html.Div([
                    # 左侧边栏和主内容区
                    html.Div([
                        # 侧边栏列
                        html.Div([
                            create_sidebar(),
                            create_model_selection_panel()
                        ], className="col-md-3 pe-3 desktop-column", id="sidebar-col"),

                        # 主内容列
                        html.Div([
                            create_chat_area(),
                        ], className="col-md-9 mobile-column", id="content-col"),
                    ], className="row content-row", id="main-layout-row"),
                ]),
            ], id="main-content", style={"display": "none"}, className="container-fluid"),
        ], className="content-wrapper"),

        # 管理员面板
        create_admin_panel(),

        # 各种模态窗口
        create_add_user_modal(),
        create_api_key_display_modal(),
        create_edit_message_modal(),
        create_delete_conversation_modal(),

        # 响应式布局控制
        html.Div(id="viewport-meta", style={"display": "none"}),

        # 客户端回调以处理响应式布局
        clientside_callback(
            ClientsideFunction(namespace='clientside', function_name='handleResponsiveLayout'),
            Output("viewport-meta", "children"),
            Input("clientside-iframe-script-trigger", "children"),
        ),
        # 全局存储组件
        create_global_stores(),
        # 消息事件触发器
        html.Div(id="messages-event-trigger", style={"display": "none"}),

    ], className="app-container")
