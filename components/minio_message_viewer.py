# components/minio_message_viewer.py - MinIO消息查看器组件
import json
from dash import html, dcc, callback, Input, Output, State
import dash_bootstrap_components as dbc
from db_operator import get_rendered_content

def create_minio_message_component(msg, mathjax=True, model=None):
    """创建基于MinIO的消息组件"""
    
    # 确定消息类型和样式
    if msg["role"] == "user":
        message_class = "user-message"
        avatar = html.Div(html.I(className="fas fa-user"), className="message-avatar user-avatar")
        role_label = "用户"
    else:
        message_class = "assistant-message"
        avatar = html.Div(html.I(className="fas fa-robot"), className="message-avatar assistant-avatar")
        role_label = "AI"

    # 获取模型信息
    model_name = "未知模型"
    if model:
        model_name = model.get("display_name", model.get("name", "未知模型"))
    elif msg.get("model_name"):
        model_name = msg["model_name"]
    elif msg.get("model_id"):
        model_name = "AI助手"

    # 获取MinIO中的渲染内容URL
    rendered_url = get_rendered_content(msg["id"], mathjax)
    
    # 创建内容组件
    content_components = []
    
    # 添加模型信息（仅对AI消息）
    if msg["role"] == "assistant":
        model_info = html.Div(
            f"模型: {model_name} • 温度: {msg.get('temperature', '0.7')}",
            className="message-metadata"
        )
        content_components.append(model_info)
    
    # 如果有MinIO URL，使用iframe显示
    if rendered_url:
        iframe_component = html.Iframe(
            src=rendered_url,
            style={
                "width": "100%",
                "min-height": "200px",
                "border": "none",
                "border-radius": "8px",
                "background": "white",
                "box-shadow": "0 2px 4px rgba(0,0,0,0.1)"
            },
            id=f"minio-iframe-{msg['id']}-{'katex' if mathjax else 'plain'}",
            className="minio-content-frame"
        )
        content_components.append(iframe_component)
        
        # 添加MathJax切换按钮（仅对AI消息）
        if msg["role"] == "assistant":
            toggle_button = dbc.Button(
                [
                    html.I(className="fas fa-square-root-alt me-1"),
                    "KaTeX" if mathjax else "纯文本"
                ],
                id=f"mathjax-toggle-{msg['id']}",
                size="sm",
                color="outline-secondary",
                className="mathjax-toggle-btn",
                style={"margin-top": "10px"}
            )
            content_components.append(toggle_button)
    else:
        # 如果没有MinIO URL，显示加载中或错误信息
        loading_component = dbc.Alert(
            [
                html.I(className="fas fa-spinner fa-spin me-2"),
                "内容正在处理中，请稍候..."
            ],
            color="info",
            className="mb-2"
        )
        content_components.append(loading_component)
    
    # 创建操作按钮
    action_buttons = []
    
    # 编辑按钮（仅对用户消息）
    if msg["role"] == "user":
        edit_button = dbc.Button(
            html.I(className="fas fa-edit"),
            id=f"edit-message-{msg['id']}",
            size="sm",
            color="outline-primary",
            className="message-action-btn",
            title="编辑消息"
        )
        action_buttons.append(edit_button)
    
    # 删除按钮
    delete_button = dbc.Button(
        html.I(className="fas fa-trash"),
        id=f"delete-message-{msg['id']}",
        size="sm",
        color="outline-danger",
        className="message-action-btn",
        title="删除消息"
    )
    action_buttons.append(delete_button)
    
    # 复制按钮
    copy_button = dbc.Button(
        html.I(className="fas fa-copy"),
        id=f"copy-message-{msg['id']}",
        size="sm",
        color="outline-secondary",
        className="message-action-btn",
        title="复制内容"
    )
    action_buttons.append(copy_button)
    
    # 组装最终的消息组件
    message_component = html.Div([
        html.Div([
            # 头像
            avatar,
            # 消息内容区域
            html.Div([
                # 角色标签
                html.Div(role_label, className="message-role"),
                # 消息内容
                html.Div(content_components, className="message-content"),
                # 操作按钮
                html.Div(action_buttons, className="message-actions")
            ], className="message-body")
        ], className="message-inner"),
        
        # 时间戳
        html.Div(
            msg.get("created_at", ""),
            className="message-timestamp"
        )
    ], 
    className=f"message-container {message_class}",
    id=f"message-{msg['id']}"
    )
    
    return message_component

def create_mathjax_toggle_callback(app):
    """创建MathJax切换的回调函数"""
    
    @app.callback(
        [Output({"type": "minio-iframe", "message_id": Input("message_id")}, "src"),
         Output({"type": "mathjax-toggle", "message_id": Input("message_id")}, "children")],
        [Input({"type": "mathjax-toggle", "message_id": Input("message_id")}, "n_clicks")],
        [State({"type": "mathjax-toggle", "message_id": Input("message_id")}, "children"),
         State("store-user-use-mathjax", "data")],
        prevent_initial_call=True
    )
    def toggle_mathjax_display(n_clicks, current_text, user_mathjax_pref):
        if not n_clicks:
            return dash.no_update, dash.no_update
        
        # 获取消息ID
        ctx = dash.callback_context
        if not ctx.triggered:
            return dash.no_update, dash.no_update
        
        button_id = ctx.triggered[0]["prop_id"].split(".")[0]
        message_id = json.loads(button_id)["message_id"]
        
        # 切换MathJax状态
        current_is_katex = "KaTeX" in str(current_text)
        new_mathjax = not current_is_katex
        
        # 获取新的URL
        new_url = get_rendered_content(message_id, new_mathjax)
        
        # 更新按钮文本
        new_button_text = [
            html.I(className="fas fa-square-root-alt me-1"),
            "KaTeX" if new_mathjax else "纯文本"
        ]
        
        return new_url, new_button_text

def register_minio_message_callbacks(app):
    """注册MinIO消息相关的回调函数"""
    create_mathjax_toggle_callback(app)
    
    # 可以在这里添加更多回调函数，如：
    # - 消息删除回调
    # - 消息编辑回调
    # - 内容复制回调
    pass
