# InspirFlow 快速启动指南

## 🚀 5分钟快速部署

### 前置条件
- Python 3.12+
- MariaDB/MySQL 数据库
- Model API Service (端口5002) 运行中
- Record API Service (端口5003) 运行中

### 步骤1: 环境准备
```bash
# 克隆项目（如果需要）
cd InspirFlow

# 激活虚拟环境
source venv/bin/activate

# 安装依赖（如果未安装）
pip install -r requirements.txt
```

### 步骤2: 配置环境变量
```bash
# 检查 .env 文件
cat .env

# 确保包含以下配置：
# MODEL_API_KEY=sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo
# RECORD_API_KEY=admin-api-key-change-in-production
# MARIADB_PASSWORD=rw80827
```

### 步骤3: 验证API服务
```bash
# 检查Model API服务
curl http://localhost:5002/

# 检查Record API服务
curl http://localhost:5003/

# 测试Model API认证
curl -H "Authorization: Bearer sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo" \
     http://localhost:5002/api/v1/models
```

### 步骤4: 启动应用
```bash
# 启动InspirFlow
python app.py
```

### 步骤5: 访问应用
打开浏览器访问: http://localhost:8050

## ✅ 验证清单

### 启动成功标志
看到以下日志表示启动成功：
```
INFO:record_api_client:Record API客户端初始化: http://localhost:5003
Dash is running on http://0.0.0.0:8050/
INFO:dash.dash:Dash is running on http://0.0.0.0:8050/
* Serving Flask app 'app'
* Debug mode: on
```

### 功能测试
1. **访问首页**: 应该看到API密钥输入界面
2. **输入API密钥**: 使用有效的用户API密钥登录
3. **模型选择**: 应该能看到模型列表（Gemini 2.5 Pro等）
4. **发送消息**: 测试基本对话功能

## 🔧 常见问题解决

### 问题1: 端口占用
```bash
# 查找占用进程
lsof -i :8050

# 终止进程
pkill -f "python.*app.py"
```

### 问题2: API服务不可用
```bash
# 检查Docker容器状态
docker ps | grep -E "(5002|5003)"

# 重启API服务（如果使用Docker）
docker restart model-api-service
docker restart record-api-service
```

### 问题3: 数据库连接失败
```bash
# 测试数据库连接
mysql -h ************* -u root -prw80827 -e "SELECT 1;"

# 检查数据库表
mysql -h ************* -u root -prw80827 -e "SHOW TABLES;" chat_system
```

### 问题4: 模型列表为空
```bash
# 检查Model API密钥
curl -H "Authorization: Bearer sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo" \
     http://localhost:5002/api/v1/models

# 应该返回模型列表JSON
```

## 📱 使用指南

### 基本操作流程
1. **登录**: 输入API密钥 → 点击"验证"
2. **选择模型**: 在顶部选择AI模型（如GPT 4.1 mini 💎）
3. **开始对话**: 在底部输入框输入消息 → 点击发送
4. **查看历史**: 左侧边栏显示对话历史
5. **新建对话**: 点击"新对话"按钮

### 高级功能
- **图片上传**: 点击📷图标上传图片
- **模型切换**: 随时更改AI模型
- **温度调节**: 在设置中调整创造性参数
- **对话管理**: 删除、重命名对话

## 🛠️ 开发模式

### 启用调试模式
```bash
# 修改 .env 文件
DASH_DEBUG=True

# 或直接在启动时设置
DASH_DEBUG=True python app.py
```

### 查看详细日志
```bash
# 设置日志级别
LOG_LEVEL=DEBUG python app.py
```

### 热重载开发
应用支持代码热重载，修改Python文件后会自动重启。

## 🔒 安全注意事项

### 生产环境部署
1. **更改默认密钥**: 修改所有默认API密钥和密码
2. **启用HTTPS**: 配置SSL证书
3. **设置防火墙**: 限制端口访问
4. **定期备份**: 备份数据库和配置文件

### API密钥管理
- 不要在代码中硬编码API密钥
- 定期轮换API密钥
- 使用环境变量存储敏感信息

## 📊 监控和维护

### 健康检查
```bash
# 检查应用状态
curl http://localhost:8050/

# 检查API服务状态
curl http://localhost:5002/api/v1/health
curl http://localhost:5003/health
```

### 日志监控
```bash
# 实时查看应用日志
tail -f /var/log/inspirflow/app.log

# 查看错误日志
grep ERROR /var/log/inspirflow/app.log
```

### 性能监控
- 内存使用: 正常情况下200-300MB
- CPU使用: 空闲时<5%，对话时10-30%
- 响应时间: 本地API <1秒

## 🆘 获取帮助

### 日志文件位置
- 应用日志: 控制台输出
- API日志: Docker容器日志
- 数据库日志: MariaDB日志文件

### 调试技巧
1. **检查网络连接**: 确保所有服务可以互相访问
2. **验证配置**: 检查环境变量和配置文件
3. **查看日志**: 关注ERROR和WARNING级别的日志
4. **测试API**: 使用curl测试各个API端点

### 联系支持
如果遇到无法解决的问题：
1. 收集错误日志
2. 记录重现步骤
3. 检查系统环境信息
4. 提交Issue或联系技术支持

---

🎉 **恭喜！** 如果您看到这里，说明InspirFlow已经成功运行。开始享受AI对话的乐趣吧！
