# ui_helpers.py
import json
from dash import html, dcc
import dash_bootstrap_components as dbc
from utils.render_markdown import render_content


# 修改 ui_helpers.py 中的 create_message_component 函数

def create_message_component(msg, mathjax=True, model=None):
    """创建美化后的消息组件，支持多模态内容"""
    if msg["role"] == "user":
        message_class = "user-message"
        avatar = html.Div(html.I(className="fas fa-user"), className="message-avatar user-avatar")
        role_label = "用户"
    else:
        message_class = "assistant-message"
        avatar = html.Div(html.I(className="fas fa-robot"), className="message-avatar assistant-avatar")
        role_label = "AI"

    # 获取模型信息
    model_name = "未知模型"
    if model:
        model_name = model.get("display_name", model.get("name", "未知模型"))
    elif msg.get("model_name"):
        # 直接使用消息中的模型名称
        model_name = msg["model_name"]
    elif msg.get("model_id"):
        # 模型ID已废弃，使用默认名称
        model_name = "AI助手"

    # 消息内容组件
    content_items = []

    # 添加模型和温度信息 (移至内容前面)
    model_info = None
    if msg["role"] == "assistant":
        model_info = html.Div(
            f"模型: {model_name} • 温度: {msg.get('temperature', '0.7')}",
            className="message-metadata"
        )

    # 获取消息内容
    content = msg["content"]

    # 尝试从缓存获取渲染内容
    from db_operator import get_rendered_content, store_rendered_content

    rendered_html = get_rendered_content(msg["id"], mathjax)

    # 如果没有缓存的渲染内容，则进行渲染并存储
    if rendered_html is None:
        try:
            # 尝试解析JSON格式的多模态内容
            content_objects = json.loads(content)
            # 如果成功解析为JSON，处理多模态内容
            for item in content_objects:
                item_type = item.get("type")
                if item_type == "text":
                    # 处理文本
                    text_content = item.get("text", "")
                    content_items.append(render_content(text_content, detect_markdown=True, mathjax=mathjax))
                elif item_type == "image_url":
                    # 处理图片URL
                    image_url = item.get("image_url", {}).get("url", "")
                    if image_url:
                        content_items.append(html.Img(
                            src=image_url,
                            className="message-image",
                            style={"max-width": "70%", "max-height": "300px", "width": "auto", "height": "auto"}))

            # 异步存储渲染内容
            import threading
            def store_content():
                store_rendered_content(msg["id"], content)

            thread = threading.Thread(target=store_content)
            thread.daemon = True
            thread.start()

        except (json.JSONDecodeError, TypeError):
            raise ValueError("Invalid message content format")
    else:
        # 使用缓存的渲染内容
        content_items.append(html.Iframe(
            srcDoc=rendered_html,
            style={
                "width": "100%",
                "height": "40px",  # 使用较小的初始高度
                "border": "none",
                "overflow": "hidden",
                "display": "block",
                "transition": "height 0.1s"  # 平滑但快速的过渡
            },
            id=f"markdown-iframe-{msg['id']}",
            name=f"markdown-iframe-{msg['id']}",
            className="rendered-markdown-frame",
            sandbox="allow-scripts"  # 确保脚本可执行但增加安全性
        ))

    # 消息内容容器 - 包含相对定位，以便操作按钮可以相对于它定位
    message_content_inner = []

    # 添加模型信息到内容顶部(如果有)
    if model_info:
        message_content_inner.append(model_info)

    # 添加实际内容
    message_content_inner.append(
        html.Div(content_items, className="message-content-inner")
    )

    message_content = html.Div(
        [
            # 消息内容
            html.Div(message_content_inner, className="message-content-wrapper"),

            # 操作按钮
            html.Div([
                dbc.Button(
                    html.I(className="fas fa-edit"),
                    id={"type": "edit-message-btn", "index": msg["id"]},
                    size="sm",
                    color="link",
                    className="me-2 message-action-btn"
                ),
                dbc.Button(
                    html.I(className="fas fa-trash"),
                    id={"type": "delete-message-btn", "index": msg["id"]},
                    size="sm",
                    color="link",
                    className="text-danger message-action-btn"
                )
            ], className="message-actions")
        ],
        className="message-content",
        style={"position": "relative"}  # 只保留相对定位，删除任何高度限制
    )

    # 完整的消息组件
    return html.Div(
        [
            # 头像
            avatar,

            # 消息内容（包含操作按钮）
            message_content
        ],
        id={"type": "message-container", "index": msg["id"]},
        className=f"message-container {message_class}"
    )
def refresh_conversation_messages(conversation_id, mathjax=False):
    """
    从数据库获取对话消息并重新生成UI组件

    Args:
        conversation_id: 对话ID
        mathjax: 是否启用MathJax渲染

    Returns:
        tuple: (message_components, container_class_name)
    """
    from db_operator import get_conversation_messages_list
    import time

    try:
        # 从数据库获取最新的消息列表
        messages = get_conversation_messages_list(conversation_id)
        if not messages:
            return [], "messages-container"

        # 为每条消息创建UI组件
        message_components = []
        for msg in messages:
            # 不再使用模型ID获取模型信息，直接传入None
            message_components.append(create_message_component(msg, mathjax, None))

        # 生成新的类名触发MathJax重新渲染
        new_class = f"messages-container-updated-{time.time()}"

        return message_components, new_class

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"刷新消息失败: {str(e)}")
        return [], "messages-container"
