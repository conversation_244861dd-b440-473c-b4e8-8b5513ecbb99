# utils/minio_client.py - MinIO客户端和内容管理
import os
import json
import base64
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from minio import Minio
from minio.error import S3Error
import io

logger = logging.getLogger(__name__)

class MinIOContentManager:
    """MinIO内容管理器 - 处理聊天消息的存储和渲染"""
    
    def __init__(self):
        """初始化MinIO客户端"""
        self.endpoint = "43.155.146.157:7020"  # MinIO API端口（通常是较小的端口号）
        self.access_key = "admin"
        self.secret_key = "Rw80827mn@"
        self.bucket_name = "inspirflow"
        self.enabled = True

        try:
            # 初始化MinIO客户端
            self.client = Minio(
                self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=False  # 如果使用HTTPS则设为True
            )

            # 确保bucket存在（带超时）
            self._ensure_bucket_exists()

            logger.info(f"MinIO客户端初始化完成: {self.endpoint}/{self.bucket_name}")

        except Exception as e:
            logger.error(f"MinIO客户端初始化失败: {e}")
            logger.warning("MinIO功能已禁用，将使用本地缓存")
            self.enabled = False
            self.client = None
    
    def _ensure_bucket_exists(self):
        """确保bucket存在并设置公共读取权限"""
        if not self.enabled or not self.client:
            return

        try:
            # 设置较短的超时时间进行连接测试
            import socket
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(5)  # 5秒超时

            try:
                if not self.client.bucket_exists(self.bucket_name):
                    self.client.make_bucket(self.bucket_name)
                    logger.info(f"创建bucket: {self.bucket_name}")

                    # 设置bucket为公共读取权限
                    self._set_bucket_public_read_policy()
                else:
                    logger.debug(f"Bucket已存在: {self.bucket_name}")
                    # 确保bucket有正确的公共读取权限
                    self._set_bucket_public_read_policy()

            finally:
                socket.setdefaulttimeout(original_timeout)

        except Exception as e:
            logger.error(f"检查/创建bucket失败: {e}")
            self.enabled = False
            raise

    def _set_bucket_public_read_policy(self):
        """设置bucket的公共读取权限"""
        try:
            # 定义公共读取策略
            policy = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": ["s3:GetObject"],
                        "Resource": [f"arn:aws:s3:::{self.bucket_name}/*"]
                    }
                ]
            }

            import json
            policy_json = json.dumps(policy)
            self.client.set_bucket_policy(self.bucket_name, policy_json)
            logger.info(f"已设置bucket {self.bucket_name} 的公共读取权限")

        except Exception as e:
            logger.warning(f"设置bucket公共读取权限失败: {e}")
            # 不抛出异常，因为这不是致命错误
    
    def _generate_object_name(self, message_id: int, content_type: str, file_extension: str = "") -> str:
        """生成对象名称"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"messages/{message_id}/{content_type}_{timestamp}{file_extension}"
    
    def upload_original_content(self, message_id: int, content: str) -> Optional[str]:
        """
        上传原始聊天内容（包含图片base64和文字的JSON结构）

        Args:
            message_id: 消息ID
            content: 原始内容（JSON字符串，包含图片base64）

        Returns:
            上传后的URL或None
        """
        if not self.enabled or not self.client:
            logger.warning("MinIO未启用，跳过原始内容上传")
            return None

        try:
            # 生成对象名称
            object_name = self._generate_object_name(message_id, "original", ".json")
            
            # 将内容转换为字节流
            content_bytes = content.encode('utf-8')
            content_stream = io.BytesIO(content_bytes)
            
            # 上传到MinIO
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=content_stream,
                length=len(content_bytes),
                content_type="application/json"
            )
            
            # 生成访问URL
            url = f"http://{self.endpoint}/{self.bucket_name}/{object_name}"
            logger.info(f"原始内容上传成功: {url}")
            return url
            
        except Exception as e:
            logger.error(f"上传原始内容失败: {e}")
            return None
    
    def upload_rendered_html(self, message_id: int, html_content: str, with_katex: bool = True) -> Optional[str]:
        """
        上传渲染后的HTML内容

        Args:
            message_id: 消息ID
            html_content: 渲染后的HTML内容
            with_katex: 是否包含KaTeX数学渲染

        Returns:
            上传后的URL或None
        """
        if not self.enabled or not self.client:
            logger.warning("MinIO未启用，跳过HTML内容上传")
            return None

        try:
            # 生成对象名称
            content_type = "rendered_katex" if with_katex else "rendered_plain"
            object_name = self._generate_object_name(message_id, content_type, ".html")
            
            # 创建完整的HTML文档
            full_html = self._create_full_html_document(html_content, with_katex)
            
            # 将HTML转换为字节流
            html_bytes = full_html.encode('utf-8')
            html_stream = io.BytesIO(html_bytes)
            
            # 上传到MinIO
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=html_stream,
                length=len(html_bytes),
                content_type="text/html"
            )
            
            # 生成访问URL
            url = f"http://{self.endpoint}/{self.bucket_name}/{object_name}"
            logger.info(f"渲染HTML上传成功 ({'KaTeX' if with_katex else 'Plain'}): {url}")
            return url
            
        except Exception as e:
            logger.error(f"上传渲染HTML失败: {e}")
            return None
    
    def _create_full_html_document(self, content: str, with_katex: bool = True) -> str:
        """创建完整的HTML文档"""
        katex_css = """
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
        <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
        <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>
        """ if with_katex else ""
        
        katex_script = """
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\\\[", right: "\\\\]", display: true},
                        {left: "\\\\(", right: "\\\\)", display: false}
                    ]
                });
            });
        </script>
        """ if with_katex else ""
        
        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息内容</title>
    {katex_css}
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 100%;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }}
        img {{
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 10px 0;
        }}
        pre {{
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }}
        code {{
            background: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }}
        blockquote {{
            border-left: 4px solid #ddd;
            margin: 0;
            padding-left: 20px;
            color: #666;
        }}
        .katex {{
            font-size: 1.1em;
        }}
    </style>
</head>
<body>
    {content}
    {katex_script}
</body>
</html>
        """.strip()
    
    def process_message_content(self, message_id: int, content: str) -> Dict[str, Optional[str]]:
        """
        处理消息内容，生成所有需要的URL
        
        Args:
            message_id: 消息ID
            content: 消息内容（JSON字符串或普通文本）
            
        Returns:
            包含三个URL的字典: {
                'original_url': '原始内容URL',
                'rendered_katex_url': '带KaTeX渲染的HTML URL',
                'rendered_plain_url': '不带KaTeX渲染的HTML URL'
            }
        """
        try:
            from utils.render_markdown import render_content_to_html
            
            # 1. 上传原始内容
            original_url = self.upload_original_content(message_id, content)
            
            # 2. 渲染HTML内容
            html_with_katex = render_content_to_html(content, detect_markdown=True, mathjax=True)
            html_without_katex = render_content_to_html(content, detect_markdown=True, mathjax=False)
            
            # 3. 上传渲染后的HTML
            rendered_katex_url = self.upload_rendered_html(message_id, html_with_katex, with_katex=True)
            rendered_plain_url = self.upload_rendered_html(message_id, html_without_katex, with_katex=False)
            
            result = {
                'original_url': original_url,
                'rendered_katex_url': rendered_katex_url,
                'rendered_plain_url': rendered_plain_url
            }
            
            logger.info(f"消息 {message_id} 内容处理完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"处理消息内容失败: {e}")
            return {
                'original_url': None,
                'rendered_katex_url': None,
                'rendered_plain_url': None
            }
    
    def delete_message_content(self, message_id: int) -> bool:
        """
        删除消息的所有相关内容
        
        Args:
            message_id: 消息ID
            
        Returns:
            是否删除成功
        """
        try:
            # 列出该消息的所有对象
            prefix = f"messages/{message_id}/"
            objects = self.client.list_objects(self.bucket_name, prefix=prefix, recursive=True)
            
            deleted_count = 0
            for obj in objects:
                try:
                    self.client.remove_object(self.bucket_name, obj.object_name)
                    deleted_count += 1
                    logger.debug(f"删除对象: {obj.object_name}")
                except Exception as e:
                    logger.error(f"删除对象失败 {obj.object_name}: {e}")
            
            logger.info(f"消息 {message_id} 删除了 {deleted_count} 个对象")
            return deleted_count > 0
            
        except Exception as e:
            logger.error(f"删除消息内容失败: {e}")
            return False
    
    def get_message_urls(self, message_id: int) -> Dict[str, Optional[str]]:
        """
        获取消息的所有URL
        
        Args:
            message_id: 消息ID
            
        Returns:
            包含URL的字典
        """
        try:
            prefix = f"messages/{message_id}/"
            objects = self.client.list_objects(self.bucket_name, prefix=prefix, recursive=True)
            
            urls = {
                'original_url': None,
                'rendered_katex_url': None,
                'rendered_plain_url': None
            }
            
            for obj in objects:
                url = f"http://{self.endpoint}/{self.bucket_name}/{obj.object_name}"
                
                if "original" in obj.object_name:
                    urls['original_url'] = url
                elif "rendered_katex" in obj.object_name:
                    urls['rendered_katex_url'] = url
                elif "rendered_plain" in obj.object_name:
                    urls['rendered_plain_url'] = url
            
            return urls
            
        except Exception as e:
            logger.error(f"获取消息URL失败: {e}")
            return {
                'original_url': None,
                'rendered_katex_url': None,
                'rendered_plain_url': None
            }


# 创建全局实例
minio_content_manager = MinIOContentManager()
