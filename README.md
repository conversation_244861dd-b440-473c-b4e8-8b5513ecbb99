# InspirFlow - AI对话系统

## 项目概述

InspirFlow 是一个基于微服务架构的智能AI对话系统，提供多模态对话、用户管理、模型选择等功能。系统采用现代化的Web技术栈，支持实时对话、图片上传、Markdown渲染等特性。

## 系统架构

### 微服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   InspirFlow    │    │  Model API      │    │  Record API     │
│   Frontend      │    │  Service        │    │  Service        │
│   (Port 8050)   │    │  (Port 5002)    │    │  (Port 5003)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Database      │
                    │   MariaDB       │
                    │   (Port 3306)   │
                    └─────────────────┘
```

### 服务说明
- **InspirFlow Frontend (8050)**: 主应用，提供Web界面和用户交互
- **Model API Service (5002)**: 模型管理和AI对话API服务
- **Record API Service (5003)**: 用户、对话、消息记录管理API服务
- **MariaDB Database**: 数据持久化存储

## 技术栈

### 前端技术
- **Dash**: Python Web应用框架
- **Dash Bootstrap Components**: UI组件库
- **JavaScript**: 客户端交互逻辑
- **CSS**: 样式设计

### 后端技术
- **Python 3.12**: 主要编程语言
- **Flask**: Web服务器框架
- **SQLAlchemy**: ORM数据库操作
- **PyMySQL**: MySQL数据库连接器
- **Requests**: HTTP客户端库

### 数据库
- **MariaDB**: 主数据库
  - `chat_system`: 聊天相关数据
  - `model_registry`: 模型注册信息

### AI集成
- **OpenAI API**: 大语言模型接口
- **多模型支持**: GPT-4, Claude, Gemini等

## 项目结构

```
InspirFlow/
├── app.py                      # 主应用入口
├── config.py                   # 配置管理
├── .env                        # 环境变量配置
├── requirements.txt            # Python依赖
├── db_models.py               # 数据库模型定义
├── db_operator.py             # 数据库操作接口
├── record_api_client.py       # Record API客户端
├── openaiSDK.py               # OpenAI SDK封装
├── platform_adapters.py      # 平台适配器
├── callbacks/                 # Dash回调函数
│   ├── auth_callbacks.py      # 认证相关回调
│   ├── message_callbacks.py   # 消息处理回调
│   ├── model_callbacks.py     # 模型选择回调
│   ├── conversation_callbacks.py # 对话管理回调
│   ├── admin_callbacks.py     # 管理员功能回调
│   ├── image_callbacks.py     # 图片处理回调
│   └── settings_callbacks.py  # 设置相关回调
├── components/                # UI组件
│   ├── layout.py              # 主布局
│   ├── chat_area.py           # 聊天区域
│   ├── sidebar.py             # 侧边栏
│   ├── navbar.py              # 导航栏
│   ├── api_key.py             # API密钥输入
│   ├── model_selector.py      # 模型选择器
│   └── admin_panel.py         # 管理面板
├── utils/                     # 工具函数
│   ├── ui_helpers.py          # UI辅助函数
│   ├── auth.py                # 认证工具
│   ├── render_markdown.py     # Markdown渲染
│   └── image_processor.py     # 图片处理
├── styles/                    # 样式文件
│   └── theme.py               # 主题配置
├── assets/                    # 静态资源
│   ├── custom.css             # 自定义样式
│   ├── clientside.js          # 客户端JavaScript
│   └── favicon.ico            # 网站图标
└── config/                    # 配置文件
    └── models.json            # 模型配置
```

## 核心功能

### 1. 用户认证与管理
- API密钥认证系统
- 用户权限管理（普通用户/管理员）
- 用户余额和消费记录
- 账户状态管理

### 2. AI对话功能
- 多模型支持（GPT-4, Claude, Gemini等）
- 实时流式对话
- 多模态输入（文本+图片）
- 对话历史管理
- 消息编辑和删除

### 3. 模型管理
- 动态模型列表加载
- 模型参数配置（温度、最大token等）
- 模型特性标识（免费🆓、高价💎、多模态📷）
- 用户模型偏好保存

### 4. 对话管理
- 对话创建和删除
- 对话标题自动生成
- 对话历史浏览
- 对话内容搜索

### 5. 内容渲染
- Markdown格式支持
- MathJax数学公式渲染
- 代码高亮显示
- 图片显示和处理

### 6. 管理员功能
- 用户列表查看
- 用户状态管理
- 系统统计信息
- 用户余额管理

## 安装与部署

### 环境要求
- Python 3.12+
- MariaDB 10.5+
- 8GB+ RAM
- 10GB+ 磁盘空间

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd InspirFlow
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和API密钥
```

5. **初始化数据库**
```bash
python db_models.py
```

6. **启动应用**
```bash
python app.py
```

### 配置说明

#### 环境变量配置 (.env)
```env
# API服务配置
RECORD_API_BASE_URL=http://localhost:5003
RECORD_API_KEY=admin-api-key-change-in-production
MODEL_API_BASE_URL=http://localhost:5002
MODEL_API_KEY=sk-your-model-api-key

# 应用配置
DASH_HOST=0.0.0.0
DASH_PORT=8050
DASH_DEBUG=False

# 数据库配置
MARIADB_HOST=localhost
MARIADB_PORT=3306
MARIADB_USER=root
MARIADB_PASSWORD=your-password
MARIADB_CHAT_DB=chat_system

# 安全配置
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
```

## API接口

### Record API Service (端口5003)
- `GET /api/users/{user_id}` - 获取用户信息
- `POST /api/conversations` - 创建对话
- `GET /api/conversations/{id}/messages` - 获取对话消息
- `POST /api/messages` - 创建消息
- `PUT /api/messages/{id}` - 更新消息

### Model API Service (端口5002)
- `GET /api/v1/models` - 获取模型列表
- `POST /api/v1/chat/completions` - 聊天完成接口
- `GET /api/v1/health` - 健康检查

## 使用指南

### 基本使用流程

1. **访问应用**: 打开浏览器访问 `http://localhost:8050`
2. **输入API密钥**: 在首页输入有效的API密钥
3. **选择模型**: 在模型选择器中选择合适的AI模型
4. **开始对话**: 在聊天框中输入消息开始对话
5. **管理对话**: 使用侧边栏管理对话历史

### 高级功能

#### 图片上传
1. 点击聊天框旁的图片上传按钮
2. 选择要上传的图片文件
3. 图片将与文本一起发送给AI模型

#### 管理员功能
1. 使用管理员API密钥登录
2. 访问管理面板查看用户列表
3. 管理用户状态和余额

#### 模型参数调整
1. 在设置面板中调整温度参数
2. 设置最大token数量
3. 保存用户偏好设置

## 故障排除

### 常见问题

1. **端口占用错误**
```bash
# 查找占用端口的进程
lsof -i :8050
# 终止进程
kill -9 <PID>
```

2. **数据库连接失败**
- 检查MariaDB服务是否运行
- 验证数据库连接配置
- 确认数据库用户权限

3. **API服务不可用**
- 检查API服务是否启动
- 验证API密钥配置
- 查看服务日志

4. **模块导入错误**
```bash
# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

### 日志查看
应用日志会输出到控制台，包含以下信息：
- API请求和响应
- 数据库操作
- 错误和警告信息
- 用户操作记录

## 开发指南

### 代码结构说明

#### 回调函数 (callbacks/)
- 每个功能模块对应一个回调文件
- 使用Dash的Input/Output/State模式
- 支持客户端和服务端回调

#### 组件系统 (components/)
- 模块化UI组件设计
- 可复用的界面元素
- 响应式布局支持

#### 数据访问层
- `db_operator.py`: 统一的数据访问接口
- `record_api_client.py`: API客户端封装
- 完全基于API的数据访问，无直接数据库操作

### 添加新功能

1. **创建新组件**
```python
# components/new_component.py
def create_new_component():
    return html.Div([
        # 组件内容
    ])
```

2. **添加回调函数**
```python
# callbacks/new_callbacks.py
def register_new_callbacks(app):
    @app.callback(
        Output('output-id', 'children'),
        Input('input-id', 'value')
    )
    def handle_new_feature(value):
        # 处理逻辑
        return result
```

3. **注册回调**
```python
# app.py
from callbacks.new_callbacks import register_new_callbacks
register_new_callbacks(app)
```

## 安全考虑

### 认证安全
- API密钥验证
- JWT token管理
- 会话超时控制

### 数据安全
- SQL注入防护
- XSS攻击防护
- 敏感信息加密

### 网络安全
- HTTPS支持
- CORS配置
- 请求频率限制

## 性能优化

### 前端优化
- 组件懒加载
- 客户端缓存
- 异步数据加载

### 后端优化
- 数据库连接池
- API响应缓存
- 批量数据处理

### 数据库优化
- 索引优化
- 查询优化
- 连接池配置

## 版本历史

### v1.0.0 (当前版本)
- 基础对话功能
- 多模型支持
- 用户管理系统
- 管理员功能
- 图片上传支持

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: <repository-issues-url>
- 邮箱: <contact-email>

---

**注意**: 本文档会随着项目发展持续更新，请定期查看最新版本。
