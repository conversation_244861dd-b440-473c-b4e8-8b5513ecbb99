# record_api_client.py - Record API Service 客户端
import os
import logging
import requests
import time
from typing import Optional, Dict, Any, List
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class RecordAPIClient:
    """Record API Service 客户端类"""
    
    def __init__(self, base_url: str = None, api_key: str = None):
        """
        初始化Record API客户端

        Args:
            base_url: Record API服务的基础URL，默认从配置获取
            api_key: API认证密钥，默认从配置获取
        """
        from config import config
        self.base_url = base_url or config.RECORD_API_BASE_URL
        self.api_key = api_key or config.RECORD_API_KEY
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        # 移除末尾的斜杠
        self.base_url = self.base_url.rstrip('/')
        
        logger.info(f"Record API客户端初始化: {self.base_url}")
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求的通用方法
        
        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点路径
            data: 请求体数据
            params: 查询参数
            
        Returns:
            API响应数据或None（如果失败）
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=self.headers,
                json=data,
                params=params,
                timeout=30
            )
            
            if response.status_code == 200 or response.status_code == 201:
                result = response.json()
                if result.get('success'):
                    return result.get('data')
                else:
                    logger.error(f"API请求失败: {result.get('error', 'Unknown error')}")
                    return None
            else:
                logger.error(f"HTTP请求失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
    
    # =================== 用户相关API ===================
    
    def get_user_by_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """根据API密钥获取用户信息"""
        return self._make_request('GET', f'/api/v1/users/by-api-key/{api_key}')
    
    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        return self._make_request('GET', f'/api/v1/users/{user_id}')
    
    def create_user(self, api_key: str, **kwargs) -> Optional[Dict[str, Any]]:
        """创建新用户"""
        data = {'api_key': api_key, **kwargs}
        return self._make_request('POST', '/api/v1/users', data=data)
    
    def update_user(self, user_id: int, **kwargs) -> Optional[Dict[str, Any]]:
        """更新用户信息"""
        return self._make_request('PUT', f'/api/v1/users/{user_id}', data=kwargs)
    
    def get_user_stats(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户统计信息"""
        return self._make_request('GET', f'/api/v1/users/{user_id}/stats')
    
    def get_users(self, page: int = 1, per_page: int = 20, **filters) -> Optional[Dict[str, Any]]:
        """获取用户列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        return self._make_request('GET', '/api/v1/users', params=params)
    
    # =================== 对话相关API ===================
    
    def get_conversation(self, conversation_id: int) -> Optional[Dict[str, Any]]:
        """获取对话信息"""
        return self._make_request('GET', f'/api/v1/conversations/{conversation_id}')
    
    def create_conversation(self, user_id: int, title: str = None) -> Optional[Dict[str, Any]]:
        """创建新对话"""
        data = {'user_id': user_id}
        if title:
            data['title'] = title
        return self._make_request('POST', '/api/v1/conversations', data=data)
    
    def update_conversation(self, conversation_id: int, **kwargs) -> Optional[Dict[str, Any]]:
        """更新对话信息"""
        return self._make_request('PUT', f'/api/v1/conversations/{conversation_id}', data=kwargs)
    
    def delete_conversation(self, conversation_id: int) -> bool:
        """删除对话"""
        result = self._make_request('DELETE', f'/api/v1/conversations/{conversation_id}')
        return result is not None
    
    def get_user_conversations(self, user_id: int, page: int = 1, per_page: int = 20) -> Optional[Dict[str, Any]]:
        """获取用户的对话列表"""
        params = {'page': page, 'per_page': per_page}
        return self._make_request('GET', f'/api/v1/users/{user_id}/conversations', params=params)
    
    def get_conversation_messages(self, conversation_id: int, page: int = 1, per_page: int = 100) -> Optional[Dict[str, Any]]:
        """获取对话的消息列表"""
        params = {'page': page, 'per_page': per_page}
        return self._make_request('GET', f'/api/v1/conversations/{conversation_id}/messages', params=params)
    
    def get_conversation_stats(self, conversation_id: int) -> Optional[Dict[str, Any]]:
        """获取对话统计信息"""
        return self._make_request('GET', f'/api/v1/conversations/{conversation_id}/stats')
    
    # =================== 消息相关API ===================
    
    def get_message(self, message_id: int) -> Optional[Dict[str, Any]]:
        """获取消息信息"""
        return self._make_request('GET', f'/api/v1/messages/{message_id}')
    
    def create_message(self, conversation_id: int, role: str, content: str, model_id: int, **kwargs) -> Optional[Dict[str, Any]]:
        """创建新消息"""
        data = {
            'conversation_id': conversation_id,
            'role': role,
            'content': content,
            'model_id': model_id,
            **kwargs
        }
        return self._make_request('POST', '/api/v1/messages', data=data)
    
    def update_message(self, message_id: int, **kwargs) -> Optional[Dict[str, Any]]:
        """更新消息信息"""
        return self._make_request('PUT', f'/api/v1/messages/{message_id}', data=kwargs)
    
    def delete_message(self, message_id: int) -> bool:
        """删除消息"""
        result = self._make_request('DELETE', f'/api/v1/messages/{message_id}')
        return result is not None
    
    def create_messages_batch(self, messages: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """批量创建消息"""
        data = {'messages': messages}
        return self._make_request('POST', '/api/v1/messages/batch', data=data)
    
    def search_messages(self, keyword: str, **filters) -> Optional[Dict[str, Any]]:
        """搜索消息"""
        params = {'keyword': keyword, **filters}
        return self._make_request('GET', '/api/v1/messages/search', params=params)
    
    # =================== 系统API ===================
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def api_health_check(self) -> Optional[Dict[str, Any]]:
        """API健康检查"""
        return self._make_request('GET', '/api/v1/health')
    
    def get_api_info(self) -> Optional[Dict[str, Any]]:
        """获取API信息"""
        return self._make_request('GET', '/api/v1/info')

    # =================== 交易相关API ===================

    def deposit_to_user(self, user_id: int, amount: float, description: str = None) -> Optional[Dict[str, Any]]:
        """为用户充值"""
        data = {
            'user_id': user_id,
            'amount': amount,
            'transaction_type': 'deposit',
            'description': description or f"充值 ${amount}"
        }
        return self._make_request('POST', '/api/v1/transactions', data=data)

    def get_user_transactions(self, user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户的交易记录"""
        params = {
            'user_id': user_id,
            'limit': limit,
            'offset': offset
        }
        result = self._make_request('GET', '/api/v1/transactions', params=params)
        if result and 'transactions' in result:
            return result['transactions']
        elif result and 'items' in result:
            return result['items']
        return []

    # =================== 辅助方法 ===================

    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """验证API密钥并返回用户信息（兼容原有接口）"""
        return self.get_user_by_api_key(api_key)

    def get_user_conversations_list(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户对话列表（兼容原有接口）"""
        result = self.get_user_conversations(user_id, per_page=100)
        if result and 'conversations' in result:
            return result['conversations']
        elif result and 'items' in result:
            return result['items']
        return []

    def get_conversation_messages_list(self, conversation_id: int) -> List[Dict[str, Any]]:
        """获取对话消息列表（兼容原有接口）"""
        result = self.get_conversation_messages(conversation_id, per_page=1000)
        if result and 'messages' in result:
            return result['messages']
        elif result and 'items' in result:
            return result['items']
        return []

    def load_conversation_messages_by_id(self, conversation_id: int) -> List[Dict[str, str]]:
        """加载对话历史，转换为AI接口格式（兼容原有接口）"""
        messages = self.get_conversation_messages_list(conversation_id)
        result = []
        for msg in messages:
            if not msg.get('is_error', False):  # 过滤错误消息
                result.append({
                    "role": msg.get('role', ''),
                    "content": msg.get('content', '')
                })
        return result

    def get_user_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户数据（兼容原有接口）"""
        return self.get_user(user_id)

    def get_conversation_data(self, conversation_id: int) -> Optional[Dict[str, Any]]:
        """获取对话数据（兼容原有接口）"""
        return self.get_conversation(conversation_id)

    def get_message_data(self, message_id: int) -> Optional[Dict[str, Any]]:
        """获取消息数据（兼容原有接口）"""
        return self.get_message(message_id)

    def create_new_conversation(self, user_id: int, title: str = '新对话') -> Optional[Dict[str, Any]]:
        """创建新对话（兼容原有接口）"""
        return self.create_conversation(user_id, title)

    def update_conversation_title(self, conversation_id: int, title: str) -> bool:
        """更新对话标题（兼容原有接口）"""
        result = self.update_conversation(conversation_id, title=title)
        return result is not None

    def set_current_conversation(self, user_id: int, conversation_id: int) -> bool:
        """设置用户当前对话（兼容原有接口）"""
        result = self.update_user(user_id, current_conversation_id=conversation_id)
        return result is not None

    def get_user_temperature(self, user_id: int, default: float = 0.7) -> float:
        """获取用户当前温度设置（兼容原有接口）"""
        user_data = self.get_user_data(user_id)
        if user_data:
            return float(user_data.get("current_temperature", default))
        return default

    def get_user_model_name(self, user_id: int, default: str = "gpt-3.5-turbo") -> str:
        """获取用户当前模型名称（兼容原有接口）"""
        user_data = self.get_user_data(user_id)
        if user_data:
            return user_data.get("current_model_name", default)
        return default

    def get_user_statistics(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户统计数据（兼容原有接口）"""
        return self.get_user_stats(user_id)

    def update_message_token_count(self, message_id: int, prompt_tokens: int = None,
                                 completion_tokens: int = None, total_cost: float = None) -> bool:
        """更新消息token统计（兼容原有接口）"""
        update_data = {}
        if prompt_tokens is not None:
            update_data['prompt_tokens'] = prompt_tokens
        if completion_tokens is not None:
            update_data['completion_tokens'] = completion_tokens
        if total_cost is not None:
            update_data['total_cost'] = total_cost

        if update_data:
            result = self.update_message(message_id, **update_data)
            return result is not None
        return True

    def calculate_message_cost(self, prompt_tokens: int, completion_tokens: int,
                             input_price: float, output_price: float) -> Dict[str, float]:
        """计算消息费用（兼容原有接口）"""
        prompt_cost = (prompt_tokens / 1000) * input_price
        completion_cost = (completion_tokens / 1000) * output_price
        total_cost = prompt_cost + completion_cost

        return {
            'prompt_cost': prompt_cost,
            'completion_cost': completion_cost,
            'total_cost': total_cost
        }

    def store_rendered_content(self, message_id: int, rendered_with_mathjax: str,
                             rendered_without_mathjax: str) -> Optional[Dict[str, Any]]:
        """存储消息的渲染内容（本地缓存实现）"""
        try:
            # 由于Record API不支持渲染内容端点，使用本地缓存
            import os
            import json
            import zlib

            # 创建缓存目录
            cache_dir = os.path.join(os.getcwd(), 'cache', 'rendered_content')
            os.makedirs(cache_dir, exist_ok=True)

            # 压缩内容以节省空间
            compressed_with_mathjax = zlib.compress(rendered_with_mathjax.encode('utf-8'))
            compressed_without_mathjax = zlib.compress(rendered_without_mathjax.encode('utf-8'))

            # 存储到文件
            cache_file = os.path.join(cache_dir, f'message_{message_id}.json')
            cache_data = {
                'message_id': message_id,
                'rendered_with_mathjax': compressed_with_mathjax.hex(),  # 转换为hex字符串存储
                'rendered_without_mathjax': compressed_without_mathjax.hex(),
                'created_at': time.time()
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f)

            logger.debug(f"渲染内容已缓存到本地: {cache_file}")
            return {'status': 'cached', 'file': cache_file}
        except Exception as e:
            logger.error(f"存储渲染内容失败: {e}")
            return None

    def get_rendered_content(self, message_id: int, mathjax: bool = True) -> Optional[str]:
        """获取消息的渲染内容（本地缓存实现）"""
        try:
            import os
            import json
            import zlib

            # 检查本地缓存
            cache_dir = os.path.join(os.getcwd(), 'cache', 'rendered_content')
            cache_file = os.path.join(cache_dir, f'message_{message_id}.json')

            if not os.path.exists(cache_file):
                return None

            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 选择适当的渲染内容
            if mathjax:
                compressed_hex = cache_data.get('rendered_with_mathjax')
            else:
                compressed_hex = cache_data.get('rendered_without_mathjax')

            if compressed_hex:
                # 从hex字符串恢复并解压缩
                compressed_data = bytes.fromhex(compressed_hex)
                return zlib.decompress(compressed_data).decode('utf-8')

            return None
        except Exception as e:
            logger.error(f"获取渲染内容失败: {e}")
            return None

    def clear_rendered_content(self, message_id: int) -> Optional[Dict[str, Any]]:
        """清除消息的渲染缓存（本地缓存实现）"""
        try:
            import os

            # 删除本地缓存文件
            cache_dir = os.path.join(os.getcwd(), 'cache', 'rendered_content')
            cache_file = os.path.join(cache_dir, f'message_{message_id}.json')

            if os.path.exists(cache_file):
                os.remove(cache_file)
                logger.debug(f"已清除渲染缓存: {cache_file}")
                return {'status': 'cleared', 'file': cache_file}
            else:
                return {'status': 'not_found'}
        except Exception as e:
            logger.error(f"清除渲染内容失败: {e}")
            return None


# 创建全局客户端实例
record_api_client = RecordAPIClient()
