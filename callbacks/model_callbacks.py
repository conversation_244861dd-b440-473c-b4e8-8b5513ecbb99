# model_callbacks.py 负责模型选择、模型保存、模型参数更新等功能
from dash import Input, Output, State
from dash.exceptions import PreventUpdate
import logging
import requests
import json

# 修改导入，移除直接数据库操作
from db_operator import (
    update_user_model_preference,
    get_user_data,
    get_user_model_id
)

# 初始化日志
logger = logging.getLogger(__name__)

def get_available_models_from_api():
    """从API服务获取可用模型列表"""
    try:
        from config import config

        headers = {
            "Authorization": f"Bearer {config.MODEL_API_KEY}",
            "Content-Type": "application/json"
        }

        api_url = f"{config.MODEL_API_BASE_URL}/api/v1/models"
        response = requests.get(api_url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            return data.get("models", [])
        else:
            logger.error(f"获取模型列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        logger.error(f"获取模型列表异常: {e}")
        return []

def register_model_callbacks(app):
    @app.callback(
        [Output("model-selector", "options"),
         Output("model-selector", "value"),
         Output("store-user-current-model-name", "data")],  # 改为存储模型名称
        [Input("store-user-id", "data")],
        prevent_initial_call=True
    )
    def load_models_callback(user_id):
        if not user_id:
            raise PreventUpdate
        try:
            # 使用API服务获取模型列表
            models = get_available_models_from_api()
            # 获取用户信息
            user = get_user_data(user_id)

            if not user:
                raise Exception("无法获取用户数据")

            # 使用当前模型名称，如果没有则使用默认模型
            current_model_name = user.get("current_model_name", "gpt-3.5-turbo")
            if not current_model_name and models:
                current_model_name = models[0].get("name", "gpt-3.5-turbo")

            options = []
            for model in models:
                # API服务返回的模型格式: {"id": 1, "name": "gpt-4-turbo", "is_default": true}
                model_name = model.get("name", "未知模型")
                
                # 从模型名称判断模型特性
                is_free = model.get("is_default", False)  # API服务中的is_default表示是否为默认模型
                is_high_price = any(keyword in model_name.lower() for keyword in ["opus", "gpt-4", "claude-3.5", "claude-3.7"])
                is_visible_model = any(keyword in model_name.lower() for keyword in ["vision", "gpt-4o", "claude-3", "gemini", "glm-4v"])
                
                # 检查是否为免费模型
                is_free_model = any(keyword in model_name.lower() for keyword in ["free", ":free"]) or model.get("is_default", False)
                
                appendix = ""
                # 为模型添加不同的标记
                if is_free_model:
                    appendix += " 🆓"  # 免费模型
                if is_high_price:
                    appendix += " 💎"  # 高价模型
                if is_visible_model:
                    appendix += " 📷"  # 多模态输入模型
                
                # 添加标记到显示名称
                final_display_name = model_name + appendix

                options.append({
                    "label": final_display_name,
                    "value": model_name  # 使用模型名称作为值
                })

            return options, current_model_name, current_model_name
        except Exception as e:
            logger.error(f"加载模型列表失败: {str(e)}")
            return [], None, None

    @app.callback(
        Output("store-user-current-model-name", "data", allow_duplicate=True),
        [Input("model-selector", "value")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def update_user_model_callback(model_name, user_id):
        if not model_name or not user_id:
            raise PreventUpdate

        try:
            # 更新用户的当前模型名称到数据库
            from db_operator import update_user_model_name_preference
            success = update_user_model_name_preference(user_id, model_name)
            if not success:
                logger.warning(f"更新用户模型偏好失败: user_id={user_id}, model_name={model_name}")
            
            return model_name
        except Exception as e:
            logger.error(f"更新用户模型偏好异常: {e}")
            return model_name

    @app.callback(
        Output("store-user-current-temperature", "data"),
        [Input("temperature-slider", "value")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def update_user_temperature_callback(temperature, user_id):
        if temperature is None or not user_id:
            raise PreventUpdate

        try:
            # 使用db_operator更新用户温度设置
            from db_operator import update_user_temperature_preference
            success = update_user_temperature_preference(user_id, temperature)
            if not success:
                raise PreventUpdate
            return temperature
        except Exception as e:
            logger.error(f"更新用户温度设置失败: {str(e)}")
            raise PreventUpdate
