#!/usr/bin/env python3
# test_minio_integration.py - MinIO集成功能测试脚本

import json
import time
from utils.minio_client import minio_content_manager
from record_api_client import record_api_client

def test_minio_connection():
    """测试MinIO连接"""
    print("🔗 测试MinIO连接...")
    print(f"MinIO启用状态: {minio_content_manager.enabled}")
    
    if minio_content_manager.enabled:
        print("✅ MinIO连接成功!")
        return True
    else:
        print("❌ MinIO连接失败!")
        return False

def test_content_upload():
    """测试内容上传功能"""
    print("\n📤 测试内容上传功能...")
    
    # 测试消息ID
    test_message_id = 9999
    
    # 测试多模态内容
    test_content = json.dumps([
        {
            "type": "text",
            "text": "这是一个测试消息，包含**粗体**和*斜体*文字，以及数学公式：$E = mc^2$"
        },
        {
            "type": "text", 
            "text": "还有一个复杂的数学公式：$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$"
        }
    ], ensure_ascii=False)
    
    print(f"测试内容: {test_content[:100]}...")
    
    # 使用MinIO内容管理器处理消息
    urls = minio_content_manager.process_message_content(test_message_id, test_content)
    
    print(f"处理结果:")
    print(f"  原始内容URL: {urls.get('original_url')}")
    print(f"  KaTeX渲染URL: {urls.get('rendered_katex_url')}")
    print(f"  纯文本渲染URL: {urls.get('rendered_plain_url')}")
    
    # 检查所有URL是否生成成功
    success = all(urls.values())
    if success:
        print("✅ 内容上传成功!")
        return urls
    else:
        print("❌ 内容上传失败!")
        return None

def test_url_access(urls):
    """测试URL访问"""
    print("\n🌐 测试URL访问...")
    
    import requests
    
    for url_type, url in urls.items():
        if url:
            try:
                response = requests.head(url, timeout=10)
                if response.status_code == 200:
                    print(f"✅ {url_type}: 访问成功 (状态码: {response.status_code})")
                else:
                    print(f"❌ {url_type}: 访问失败 (状态码: {response.status_code})")
            except Exception as e:
                print(f"❌ {url_type}: 访问异常 - {e}")
        else:
            print(f"❌ {url_type}: URL为空")

def test_api_integration():
    """测试API集成"""
    print("\n🔌 测试API集成...")
    
    test_message_id = 8888
    test_content = "这是一个API集成测试消息，包含数学公式：$\\alpha + \\beta = \\gamma$"
    
    try:
        # 使用record_api_client处理消息内容
        result = record_api_client.process_and_store_message_content(test_message_id, test_content)
        
        if result:
            print("✅ API集成测试成功!")
            print(f"返回结果: {result}")
            return True
        else:
            print("❌ API集成测试失败!")
            return False
            
    except Exception as e:
        print(f"❌ API集成测试异常: {e}")
        return False

def test_content_deletion():
    """测试内容删除功能"""
    print("\n🗑️ 测试内容删除功能...")
    
    test_message_id = 9999
    
    try:
        # 删除测试消息的所有内容
        success = minio_content_manager.delete_message_content(test_message_id)
        
        if success:
            print("✅ 内容删除成功!")
            return True
        else:
            print("❌ 内容删除失败!")
            return False
            
    except Exception as e:
        print(f"❌ 内容删除异常: {e}")
        return False

def test_fallback_behavior():
    """测试降级行为"""
    print("\n🔄 测试降级行为...")
    
    # 临时禁用MinIO
    original_enabled = minio_content_manager.enabled
    minio_content_manager.enabled = False
    
    try:
        test_content = "这是一个降级测试消息"
        result = minio_content_manager.upload_original_content(7777, test_content)
        
        if result is None:
            print("✅ 降级行为正常 - MinIO禁用时返回None")
            success = True
        else:
            print("❌ 降级行为异常 - MinIO禁用时仍返回结果")
            success = False
            
    finally:
        # 恢复MinIO状态
        minio_content_manager.enabled = original_enabled
        
    return success

def main():
    """主测试函数"""
    print("🚀 开始MinIO集成功能测试\n")
    
    test_results = []
    
    # 1. 测试MinIO连接
    test_results.append(("MinIO连接", test_minio_connection()))
    
    if test_results[0][1]:  # 如果连接成功，继续其他测试
        
        # 2. 测试内容上传
        urls = test_content_upload()
        test_results.append(("内容上传", urls is not None))
        
        if urls:
            # 3. 测试URL访问
            test_url_access(urls)
            test_results.append(("URL访问", True))  # 简化处理
        
        # 4. 测试API集成
        test_results.append(("API集成", test_api_integration()))
        
        # 5. 测试内容删除
        test_results.append(("内容删除", test_content_deletion()))
        
        # 6. 测试降级行为
        test_results.append(("降级行为", test_fallback_behavior()))
    
    # 输出测试总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! MinIO集成功能正常!")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
